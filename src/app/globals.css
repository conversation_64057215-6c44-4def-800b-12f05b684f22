@import "tailwindcss";
@import "tw-animate-css";

/* Adobe Typekit Fonts */
.font-gooddog {
  font-family: "gooddog-new", "GoodDog New", sans-serif !important;
}

.font-obviously {
  font-family: "obviously", "Obviously", sans-serif !important;
}

.font-narrow {
  font-family: "obviously-narrow", "Obviously Narrow", sans-serif-condensed,
    sans-serif !important;
}

.font-verveine {
  font-family: "verveine", "verveine-regular", "Verveine", "Verveine Regular",
    cursive, sans-serif !important;
  font-weight: 400 !important; /* Ensure regular weight */
  font-style: normal !important; /* Ensure normal style, not italic */
}

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  .rotate-y-180 {
    transform: rotateY(180deg);
  }

  /* 3D Transform Support */
  .backface-hidden {
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }

  /* Hide scrollbar for Chrome, Safari and Opera */
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .scrollbar-hide {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  /* Custom scrollbar styling */
  .carousel-content {
    /* Apply custom scrollbar styling only to carousel content */
    scrollbar-width: thin;
    scrollbar-color: var(--scrollbar-thumb-color, #888)
      var(--scrollbar-track-color, #fff7e7);
    /* Force scrollbar to always be visible in Firefox */
    scrollbar-gutter: stable;
  }

  /* Only add padding when scrollbar is actually visible */
  .carousel-content[data-scrollable="true"] {
    /* Add padding to create space for the scrollbar */
    padding-bottom: 24px !important;
  }

  /* Style the scrollbar for WebKit browsers */
  .carousel-content::-webkit-scrollbar {
    height: 6px;
    width: 6px;
    /* Force scrollbar to always be visible */
    -webkit-appearance: none;
  }

  /* Style the track (the area the thumb moves along) */
  .carousel-content::-webkit-scrollbar-track {
    background-color: #fff7e7;
    border-radius: 3px;
    /* These properties help ensure the track is the same height as the thumb */
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0);
    /* Force track to always be visible */
    -webkit-appearance: none;
  }

  /* Style the thumb (the draggable scrolling element) */
  .carousel-content::-webkit-scrollbar-thumb {
    background-color: var(--scrollbar-thumb-color, #888);
    border-radius: 3px;
    /* Force thumb to always be visible and prevent auto-hide */
    -webkit-appearance: none;
    /* Ensure minimum size so thumb is always visible */
    min-height: 20px;
    min-width: 20px;
  }

  /* Force scrollbar to always be visible on hover and focus */
  .carousel-content::-webkit-scrollbar-thumb:hover {
    background-color: var(--scrollbar-thumb-color, #888);
    opacity: 1;
  }

  /* Prevent scrollbar from hiding on inactive state */
  .carousel-content::-webkit-scrollbar-thumb:window-inactive {
    background-color: var(--scrollbar-thumb-color, #888);
    opacity: 1;
  }

  /* Target horizontal scrollbar specifically */
  .carousel-content::-webkit-scrollbar:horizontal {
    height: 6px;
  }

  /* Ensure the scrollbar appears at the bottom with proper spacing */
  .carousel-content {
    overflow-y: hidden !important;
    overflow-x: auto !important;
    /* Force scrollbar to always be visible and prevent auto-hide */
    scrollbar-gutter: stable both-edges;
  }

  /* Additional styles to ensure persistent scrollbar visibility */
  .carousel-content::-webkit-scrollbar-track:horizontal {
    /* Always show horizontal track */
    display: block !important;
    visibility: visible !important;
  }

  .carousel-content::-webkit-scrollbar-thumb:horizontal {
    /* Always show horizontal thumb */
    display: block !important;
    visibility: visible !important;
    /* Ensure thumb has minimum opacity to always be visible */
    opacity: 1 !important;
  }

  /* Force scrollbar to be always visible on all states */
  .carousel-content::-webkit-scrollbar-thumb:horizontal:active,
  .carousel-content::-webkit-scrollbar-thumb:horizontal:hover,
  .carousel-content::-webkit-scrollbar-thumb:horizontal:focus {
    opacity: 1 !important;
    visibility: visible !important;
  }

  /* Marquee animations */
  .animate-marquee-left {
    animation: marquee-left 25s linear infinite;
  }

  .animate-marquee-up {
    animation: marquee-up 25s linear infinite;
  }

  .direction-reverse {
    animation-direction: reverse;
  }

  @keyframes marquee-left {
    0% {
      transform: translateX(0%);
    }
    100% {
      transform: translateX(calc(-100% - var(--gap, 1rem)));
    }
  }

  @keyframes marquee-up {
    0% {
      transform: translateY(0%);
    }
    100% {
      transform: translateY(calc(-100% - var(--gap, 1rem)));
    }
  }
}
